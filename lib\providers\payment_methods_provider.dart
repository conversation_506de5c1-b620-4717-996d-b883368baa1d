import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/payment_method.dart';
import '../utils/logger_utils.dart';
// 로컬 전용 모드: 사용하지 않는 import 제거됨
// 로컬 전용 모드: 사용하지 않는 import 제거됨

class PaymentMethodsState {
  final List<PaymentMethod> methods;
  final bool isLoading;
  final String? error;

  const PaymentMethodsState({
    this.methods = const [],
    this.isLoading = false,
    this.error,
  });

  PaymentMethodsState copyWith({
    List<PaymentMethod>? methods,
    bool? isLoading,
    String? error,
  }) => PaymentMethodsState(
        methods: methods ?? this.methods,
        isLoading: isLoading ?? this.isLoading,
        error: error,
      );

  /// 활성화된 결제수단만 반환
  List<PaymentMethod> get activeMethods =>
      methods.where((m) => m.isActive).toList();
}

class PaymentMethodsNotifier extends StateNotifier<PaymentMethodsState> {
  static const _tag = 'PaymentMethodsProvider';

  final FirebaseAuth _auth = FirebaseAuth.instance;
  // 로컬 전용 모드: Firestore 사용하지 않음
  StreamSubscription? _authSub;
  StreamSubscription<DocumentSnapshot>? _pmDocSub;

  PaymentMethodsNotifier() : super(const PaymentMethodsState()) {
    _start();
  }

  void _start() {
    // 중복 구독 방지
    if (_authSub != null) {
      LoggerUtils.logInfo('PaymentMethods 이미 초기화됨 - 중복 실행 방지', tag: _tag);
      return;
    }

    // 인증 상태를 감지하여 초기 로드만 수행 (실시간 구독 제거)
    _authSub = _auth.authStateChanges().listen((user) async {
      await _loadInitialData(user);
    });
    // 현재 사용자에 대해서도 즉시 한번 시도
    _loadInitialData(_auth.currentUser);
  }

  /// 초기 데이터 로드 (실시간 구독 없이)
  Future<void> _loadInitialData(User? user) async {
    // 기존 구독 해제
    await _pmDocSub?.cancel();
    _pmDocSub = null;

    if (user == null) {
      LoggerUtils.logDebug('로그인 사용자 없음 - 결제수단 초기화', tag: _tag);
      state = const PaymentMethodsState(methods: []);
      return;
    }

    // 로컬 전용 모드: 항상 기본 결제 방법 사용
    LoggerUtils.logInfo('로컬 전용 모드: 기본 결제 방법 사용', tag: _tag);
    state = state.copyWith(methods: _defaultMethods(), isLoading: false, error: null);
  }

  List<PaymentMethod> _defaultMethods() => [
        PaymentMethod(id: 'cash', name: '현금', order: 0, isActive: true),
        PaymentMethod(id: 'transfer', name: '계좌이체', order: 1, isActive: true),
        PaymentMethod(id: 'card', name: '카드', order: 2, isActive: false),
      ];

  // 로컬 전용 모드: 사용하지 않는 메서드 완전 삭제

  // 로컬 전용 모드: 사용하지 않는 메서드 완전 삭제

  /// 결제수단 활성화/비활성화 토글
  Future<void> toggleMethod(String id) async {
    final currentMethods = state.methods;
    final activeCount = currentMethods.where((m) => m.isActive).length;
    final targetMethod = currentMethods.firstWhere((m) => m.id == id);

    // 활성화된 결제수단이 1개뿐이고 그것을 비활성화하려는 경우 방지
    if (activeCount <= 1 && targetMethod.isActive) return;

    final newList = currentMethods.map((m) =>
      m.id == id ? m.copyWith(isActive: !m.isActive) : m
    ).toList();

    await _save(newList);
  }





  Future<void> reorder(List<PaymentMethod> ordered) async {
    final newList = [
      for (int i = 0; i < ordered.length && i < 3; i++)
        ordered[i].copyWith(order: i)
    ];
    if (newList.isEmpty) return;
    await _save(newList);
  }

  Future<void> _save(List<PaymentMethod> list) async {
    final user = _auth.currentUser;
    if (user == null) return;

    // 플랜 체크: 프리/플러스 플랜에서는 로컬 상태만 업데이트
    try {
      // 로컬 전용 모드: 사용하지 않는 변수들 제거됨
      final canUseRealtimeSync = false; // 프로 플랜 제거됨

      if (!canUseRealtimeSync) {
        LoggerUtils.logInfo('프리/플러스 플랜에서는 결제 방법을 로컬에서만 저장합니다', tag: _tag);
        state = state.copyWith(methods: list, error: null);
        return;
      }
    } catch (e) {
      LoggerUtils.logWarning('플랜 타입 확인 실패 - 로컬만 저장: $e', tag: _tag);
      state = state.copyWith(methods: list, error: null);
      return;
    }

    // 로컬 전용 모드: Firebase 저장 코드 제거됨 (Dead code 제거)
  }

  @override
  void dispose() {
    _pmDocSub?.cancel();
    _authSub?.cancel();
    super.dispose();
  }
}

final paymentMethodsProvider = StateNotifierProvider<PaymentMethodsNotifier, PaymentMethodsState>((ref) {
  final notifier = PaymentMethodsNotifier();
  ref.onDispose(() => notifier.dispose());
  return notifier;
});

