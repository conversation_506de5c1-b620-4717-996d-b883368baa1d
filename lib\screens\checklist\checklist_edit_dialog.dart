import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/checklist_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../models/checklist_template.dart';
import '../../utils/app_colors.dart';
import '../../utils/dimens.dart';

import '../../utils/toast_utils.dart';
import '../../utils/dialog_theme.dart' as custom_dialog;





/// 체크리스트 편집 다이얼로그 (새 버전)
///
/// 개선된 UX:
/// - 항상 보이는 텍스트 필드
/// - 드래그 앤 드롭으로 순서 변경
/// - 뒤로가기 버튼과 저장 버튼
/// - 일괄 저장 방식
class ChecklistEditDialog extends ConsumerStatefulWidget {
  const ChecklistEditDialog({super.key});

  @override
  ConsumerState<ChecklistEditDialog> createState() => _ChecklistEditDialogState();
}

class _ChecklistEditDialogState extends ConsumerState<ChecklistEditDialog> {
  final _titleController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  List<ChecklistTemplate> _templates = [];
  ChecklistTemplate? _editingTemplate;
  final _editController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadTemplates();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _editController.dispose();
    super.dispose();
  }

  /// 템플릿 목록 로드
  void _loadTemplates() {
    final checklistState = ref.read(checklistNotifierProvider);
    _templates = List.from(checklistState.templates);
  }

  /// 새 템플릿 추가 (즉시 저장)
  Future<void> _addTemplate() async {
    if (_titleController.text.trim().isEmpty) return;

    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) return;

    final newTemplate = ChecklistTemplate.create(
      title: _titleController.text.trim(),
      order: _templates.length,
    );

    try {
      final notifier = ref.read(checklistNotifierProvider.notifier);
      await notifier.addTemplate(newTemplate);

      setState(() {
        _templates.add(newTemplate);
        _titleController.clear();
      });
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, '체크리스트 항목 추가 중 오류가 발생했습니다: $e');
      }
    }
  }

  /// 템플릿 삭제 (즉시 저장)
  Future<void> _deleteTemplate(int index) async {
    if (index < 0 || index >= _templates.length) return;

    final templateToDelete = _templates[index];
    if (templateToDelete.id == null) return;

    try {
      final notifier = ref.read(checklistNotifierProvider.notifier);
      await notifier.deleteTemplate(templateToDelete.id!);

      setState(() {
        _templates.removeAt(index);
        // 순서 재정렬
        for (int i = 0; i < _templates.length; i++) {
          _templates[i] = _templates[i].copyWith(order: i);
        }
      });
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, '체크리스트 항목 삭제 중 오류가 발생했습니다: $e');
      }
    }
  }

  /// 템플릿 수정 시작
  void _startEditTemplate(int index) {
    setState(() {
      _editingTemplate = _templates[index];
      _editController.text = _editingTemplate!.title;
    });
  }

  /// 템플릿 수정 완료 (즉시 저장)
  Future<void> _finishEditTemplate() async {
    if (_editingTemplate != null && _editController.text.trim().isNotEmpty) {
      final index = _templates.indexWhere((t) => t.title == _editingTemplate!.title);
      if (index != -1 && _editingTemplate!.id != null) {
        final updatedTemplate = _editingTemplate!.copyWith(
          title: _editController.text.trim(),
        );

        try {
          final notifier = ref.read(checklistNotifierProvider.notifier);
          await notifier.updateTemplate(updatedTemplate);

          setState(() {
            _templates[index] = updatedTemplate;
            _editingTemplate = null;
            _editController.clear();
          });
        } catch (e) {
          if (mounted) {
            ToastUtils.showError(context, '체크리스트 항목 수정 중 오류가 발생했습니다: $e');
          }
        }
      }
    }
  }

  /// 템플릿 수정 취소
  void _cancelEditTemplate() {
    setState(() {
      _editingTemplate = null;
      _editController.clear();
    });
  }

  /// 템플릿 순서 변경 (즉시 저장)
  Future<void> _reorderTemplates(int oldIndex, int newIndex) async {
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }

    final item = _templates.removeAt(oldIndex);
    _templates.insert(newIndex, item);

    // 순서 재정렬
    final updatedTemplates = <ChecklistTemplate>[];
    for (int i = 0; i < _templates.length; i++) {
      updatedTemplates.add(_templates[i].copyWith(order: i));
    }

    try {
      final notifier = ref.read(checklistNotifierProvider.notifier);
      // 순서가 변경된 템플릿들을 병렬로 업데이트
      await Future.wait(
        updatedTemplates.where((t) => t.id != null).map((template) =>
          notifier.updateTemplate(template)
        )
      );

      setState(() {
        _templates = updatedTemplates;
      });
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, '체크리스트 순서 변경 중 오류가 발생했습니다: $e');
      }
    }
  }

  /// 뒤로가기 처리 (즉시 저장 방식이므로 확인 불필요)
  Future<void> _onBackPressed() async {
    if (mounted) Navigator.of(context).pop();
  }





  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Dialog(
        backgroundColor: AppColors.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        insetPadding: EdgeInsets.all(custom_dialog.DialogTheme.isLandscape(context) ? 8 : 16),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: BoxConstraints(
            maxHeight: custom_dialog.DialogTheme.getResponsiveMaxHeight(context),
          ),
          padding: const EdgeInsets.all(Dimens.space20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 헤더
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: AppColors.onSurfaceVariant),
                    onPressed: _onBackPressed,
                    tooltip: '뒤로가기',
                  ),
                  const SizedBox(width: Dimens.space8),
                  Text(
                    '체크리스트 편집',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: AppColors.onSurface,
                    ),
                  ),
                  const Spacer(),
                ],
              ),
              const SizedBox(height: Dimens.space16),

              // 새 항목 추가 폼
              Form(
                key: _formKey,
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _titleController,
                        decoration: InputDecoration(
                          hintText: '새 체크리스트 항목을 입력하세요',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: AppColors.onSurfaceVariant),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: AppColors.primarySeed, width: 2),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: Dimens.space12,
                            vertical: Dimens.space12,
                          ),
                        ),
                        onFieldSubmitted: (_) => _addTemplate(),
                        maxLength: 50,
                        buildCounter: (context, {required currentLength, required isFocused, maxLength}) => null,
                      ),
                    ),
                    const SizedBox(width: Dimens.space8),
                    ElevatedButton(
                      onPressed: _addTemplate,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primarySeed,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: Dimens.space16,
                          vertical: Dimens.space12,
                        ),
                      ),
                      child: const Text('추가'),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: Dimens.space16),

              // 구분선
              Container(
                height: 1,
                color: AppColors.onSurfaceVariant.withValues(alpha: 0.3),
              ),
              const SizedBox(height: Dimens.space8),

              // 진행률 표시 (중앙 정렬)
              Center(
                child: Text(
                  '${_templates.length}/50개 항목',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.onSurfaceVariant,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              const SizedBox(height: Dimens.space8),

              // 템플릿 목록 (스크롤 가능)
              Expanded(
                child: SingleChildScrollView(
                  child: _templates.isEmpty
                      ? _buildEmptyState()
                      : _buildTemplateList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 빈 상태 위젯
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.checklist,
            size: 64,
            color: AppColors.onSurfaceVariant.withValues(alpha: 0.5),
          ),
          const SizedBox(height: Dimens.space16),
          Text(
            '체크리스트가 없습니다',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: Dimens.space8),
          Text(
            '위의 입력 필드에서 새 항목을 추가해보세요',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.onSurfaceVariant.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 템플릿 목록 위젯
  Widget _buildTemplateList() {
    return ReorderableListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _templates.length,
      onReorder: _reorderTemplates,
      buildDefaultDragHandles: false, // 기본 드래그 핸들 비활성화
      proxyDecorator: (child, index, animation) {
        return AnimatedBuilder(
          animation: animation,
          builder: (BuildContext context, Widget? child) {
            final double animValue = Curves.easeInOut.transform(animation.value);
            final double elevation = lerpDouble(0, 6, animValue)!;
            final double scale = lerpDouble(1, 1.02, animValue)!;
            return Transform.scale(
              scale: scale,
              child: Material(
                elevation: elevation,
                borderRadius: BorderRadius.circular(8),
                child: child,
              ),
            );
          },
          child: child,
        );
      },
      itemBuilder: (context, index) {
        final template = _templates[index];
        return _buildTemplateItem(template, index);
      },
    );
  }

  /// 템플릿 아이템 위젯
  Widget _buildTemplateItem(ChecklistTemplate template, int index) {
    final isEditing = _editingTemplate?.title == template.title;

    return Container(
      key: ValueKey(template.title + index.toString()),
      margin: const EdgeInsets.only(bottom: Dimens.space6),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isEditing
              ? AppColors.primarySeed
              : AppColors.onSurfaceVariant.withValues(alpha: 0.3),
          width: isEditing ? 2 : 1,
        ),
      ),
      child: isEditing ? _buildEditingItem(index) : _buildNormalItem(template, index),
    );
  }

  /// 일반 상태 아이템
  Widget _buildNormalItem(ChecklistTemplate template, int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: Dimens.space12,
        vertical: Dimens.space4,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              template.title,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: AppColors.onSurface,
              ),
            ),
          ),
          // 수정 버튼
          IconButton(
            icon: const Icon(Icons.edit, size: 18),
            color: AppColors.onSurfaceVariant,
            onPressed: () => _startEditTemplate(index),
            tooltip: '수정',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            padding: const EdgeInsets.all(4),
          ),
          // 삭제 버튼
          IconButton(
            icon: const Icon(Icons.delete, size: 18),
            color: AppColors.textColorError,
            onPressed: () => _deleteTemplate(index),
            tooltip: '삭제',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            padding: const EdgeInsets.all(4),
          ),
          // 드래그 핸들
          ReorderableDragStartListener(
            index: index,
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(
                Icons.drag_handle,
                color: AppColors.onSurfaceVariant,
                size: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 편집 상태 아이템
  Widget _buildEditingItem(int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: Dimens.space12,
        vertical: Dimens.space6,
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _editController,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: AppColors.onSurface,
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              autofocus: true,
              onSubmitted: (_) => _finishEditTemplate(),
            ),
          ),
          // 완료 버튼
          IconButton(
            icon: const Icon(Icons.check, size: 18),
            color: AppColors.primarySeed,
            onPressed: _finishEditTemplate,
            tooltip: '완료',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            padding: const EdgeInsets.all(4),
          ),
          // 취소 버튼
          IconButton(
            icon: const Icon(Icons.close, size: 18),
            color: AppColors.onSurfaceVariant,
            onPressed: _cancelEditTemplate,
            tooltip: '취소',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            padding: const EdgeInsets.all(4),
          ),
        ],
      ),
    );
  }
}
