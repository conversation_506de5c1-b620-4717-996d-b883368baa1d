import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:io';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

import '../../models/event.dart';
import '../../providers/event_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../repositories/revenue_goal_repository.dart';
import '../../providers/prepayment_provider.dart';
import '../../providers/revenue_goal_provider.dart';
import '../../providers/sales_log_provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/seller_provider.dart';
import '../../providers/home_dashboard_filter_provider.dart';
import '../../utils/event_workspace_utils.dart';
import '../../utils/logger_utils.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/onboarding_components.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/image_crop_widget.dart';
import '../../widgets/unsaved_changes_dialog.dart';
import '../../utils/image_utils.dart';


/// 행사 생성/수정 화면
///
/// 새로운 행사를 생성하거나 기존 행사를 수정하는 폼 화면
class EventFormScreen extends ConsumerStatefulWidget {
  final Event? event; // null이면 새 행사 생성, 값이 있으면 수정

  const EventFormScreen({super.key, this.event});

  @override
  ConsumerState<EventFormScreen> createState() => _EventFormScreenState();
}

class _EventFormScreenState extends ConsumerState<EventFormScreen> {
  static const String _tag = 'EventFormScreen';

  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  DateTime? _startDate;
  DateTime? _endDate;
  String? _imagePath;
  bool _isLoading = false;

  bool get _isEditing => widget.event != null;

  @override
  void initState() {
    super.initState();
    LoggerUtils.methodStart('initState', tag: _tag);

    if (_isEditing) {
      _initializeWithExistingEvent();
    } else {
      _initializeForNewEvent();
    }

    LoggerUtils.methodEnd('initState', tag: _tag);
  }

  /// 기존 행사 정보로 초기화
  void _initializeWithExistingEvent() {
    final event = widget.event!;
    _nameController.text = event.name;
    _descriptionController.text = event.description ?? '';
    _startDate = event.startDate;
    _endDate = event.endDate;
    _imagePath = event.imagePath;
  }

  /// 새 행사를 위한 초기화
  void _initializeForNewEvent() {
    // 새 행사 생성시에는 날짜 초기값을 설정하지 않음
    _startDate = null;
    _endDate = null;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
  bool get _hasChanges {
    // 폼 변경 여부 간단 추정: 텍스트/날짜/이미지 중 하나라도 기존값과 다르면 true
    if (_isEditing) {
      final e = widget.event!;
      if (_nameController.text.trim() != e.name) return true;
      if (_descriptionController.text.trim() != (e.description ?? '')) return true;
      if (!(_startDate == e.startDate && _endDate == e.endDate)) return true;
      if (_imagePath != e.imagePath) return true;
      return false;
    } else {
      // 신규 생성: 아무 필드라도 입력되면 변경으로 간주
      if (_nameController.text.trim().isNotEmpty) return true;
      if (_descriptionController.text.trim().isNotEmpty) return true;
      if (_startDate != null || _endDate != null) return true;
      if (_imagePath != null && _imagePath!.isNotEmpty) return true;
      return false;
    }
  }

  Future<bool> _confirmLeaveIfNeeded() async {
    if (!_hasChanges) return true;
    final confirmed = await UnsavedChangesDialog.show(
      context: context,
    );
    return confirmed == true;
  }


  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final canLeave = await _confirmLeaveIfNeeded();
          if (!canLeave) return;
          if (mounted) Navigator.of(context).maybePop();
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppBar(),
        body: _buildBody(),
      ),
    );
  }

  /// AppBar 구성
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        _isEditing ? '행사 수정' : '새 행사',
        style: TextStyle(
          fontSize: ResponsiveHelper.getSubtitleFontSize(context),
          fontWeight: FontWeight.bold,
          color: AppColors.onboardingTextPrimary,
        ),
      ),
      centerTitle: true,
      backgroundColor: AppColors.surface,
      elevation: 0,
      iconTheme: IconThemeData(color: AppColors.onboardingTextPrimary),
    );
  }

  /// 메인 바디 구성
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    return Center(
      child: SingleChildScrollView(
        child: OnboardingComponents.buildCard(
          context: context,
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 헤더
                _buildHeader(),

                OnboardingComponents.buildSectionSpacing(context),

                // 이미지와 입력 필드가 나란히 배치되는 섹션
                _buildImageAndInputSection(),

                OnboardingComponents.buildSectionSpacing(context),

                // 날짜 선택 섹션
                _buildDateSection(),

                OnboardingComponents.buildSectionSpacing(context),

                // 저장 버튼
                _buildSaveButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 헤더 섹션
  Widget _buildHeader() {
    return Column(
      children: [
        // 제목
        OnboardingComponents.buildTitle(
          context: context,
          text: _isEditing ? '행사 수정' : '새 행사 등록',
        ),

        const SizedBox(height: 6),

        // 부제목
        OnboardingComponents.buildSubtitle(
          context: context,
          text: _isEditing ? '행사 정보를 수정하세요' : '새로운 행사를 등록하세요',
        ),
      ],
    );
  }

  /// 이미지와 입력 필드 섹션 (나란히 배치)
  Widget _buildImageAndInputSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 왼쪽: 이미지 추가
        _buildImageCropSection(),

        const SizedBox(width: 16),

        // 오른쪽: 행사명과 설명 입력
        Expanded(
          child: Column(
            children: [
              // 행사명 입력
              OnboardingComponents.buildTextField(
                context: context,
                controller: _nameController,
                label: '행사명',
                hint: '행사 이름을 입력하세요',
                prefixIcon: Icons.event,
                textInputAction: TextInputAction.next,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '행사명을 입력해주세요';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 12),

              // 메모 입력
              OnboardingComponents.buildTextField(
                context: context,
                controller: _descriptionController,
                label: '메모',
                hint: '행사에 대한 메모를 입력하세요',
                prefixIcon: Icons.description,
                textInputAction: TextInputAction.done,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 이미지 크롭 섹션 (왼쪽 배치용)
  Widget _buildImageCropSection() {
    return GestureDetector(
      onTap: _pickAndCropImage,
      child: Container(
        width: 110,
        height: 110,
        decoration: BoxDecoration(
          color: AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: _imagePath != null
            ? ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Image.file(
                  File(_imagePath!),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildImagePlaceholder();
                  },
                ),
              )
            : _buildImagePlaceholder(),
      ),
    );
  }

  /// 이미지 선택 및 크롭
  Future<void> _pickAndCropImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null) {
        // 1. 3단계 방식: 흰색 800x800 캔버스 + 이미지 최대 650px로 전처리
        final originalBytes = await image.readAsBytes();
        final paddedBytes = await addWhitePaddingAndCenterImage(originalBytes);
        final tempDir = await Directory.systemTemp.createTemp('event_crop_temp');
        final tempFile = File('${tempDir.path}/padded_${DateTime.now().millisecondsSinceEpoch}.jpg');
        await tempFile.writeAsBytes(paddedBytes);

        // 2. 크롭 다이얼로그 호출 (흰색 패딩 포함 이미지)
        final croppedFile = await ImageCropUtils.cropImage(
          context: context,
          imagePath: tempFile.path,
          shape: CropShape.roundedSquare,
          aspectRatio: 1.0,
        );

        if (croppedFile != null) {
          // 3. 크롭된 이미지를 200x200으로 리사이즈 (프로필 이미지와 동일)
          final croppedBytes = await croppedFile.readAsBytes();
          final img.Image? imgDecoded = img.decodeImage(croppedBytes);
          final img.Image imgResized = img.copyResize(imgDecoded!, width: 200, height: 200);
          final jpgBytes = img.encodeJpg(imgResized, quality: 80);

          // 4. 영구 저장소에 최종 파일 저장
          final appDocDir = await getApplicationDocumentsDirectory();
          final eventImagesDir = Directory(path.join(appDocDir.path, 'event_images'));
          if (!await eventImagesDir.exists()) {
            await eventImagesDir.create(recursive: true);
          }

          final timestamp = DateTime.now().millisecondsSinceEpoch;
          final fileName = 'event_${timestamp}.jpg';
          final finalFile = File(path.join(eventImagesDir.path, fileName));
          await finalFile.writeAsBytes(jpgBytes);

          setState(() {
            _imagePath = finalFile.path;
          });
        }
      }
    } catch (e) {
      LoggerUtils.logError('이미지 선택 실패', tag: _tag, error: e);

      if (mounted) {
        ToastUtils.showError(context, '이미지 선택에 실패했습니다');
      }
    }
  }

  /// 이미지 플레이스홀더 (작은 크기에 맞게 조정)
  Widget _buildImagePlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.add_photo_alternate_outlined,
          size: 32,
          color: AppColors.onboardingPrimary,
        ),
        const SizedBox(height: 4),
        Text(
          '이미지 추가',
          style: TextStyle(
            fontSize: 10,
            color: AppColors.onboardingPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// 날짜 섹션
  Widget _buildDateSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        OnboardingComponents.buildSubtitle(
          context: context,
          text: '행사 기간 *',
          textAlign: TextAlign.left,
          color: AppColors.onboardingTextPrimary,
        ),
        const SizedBox(height: 12),

        GestureDetector(
          onTap: () {
            // 키보드 숨기기
            FocusScope.of(context).unfocus();
            _showDateRangePicker();
          },
          child: Container(
            width: double.infinity,
            padding: ResponsiveHelper.getButtonPadding(context),
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
              border: Border.all(color: AppColors.secondary),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_month,
                  color: AppColors.onboardingPrimary,
                  size: ResponsiveHelper.getIconSize(context),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _getDateRangeText(),
                    style: TextStyle(
                      fontSize: ResponsiveHelper.getBodyFontSize(context),
                      color: AppColors.onboardingTextPrimary,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: AppColors.onboardingTextSecondary,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }





  /// 날짜 범위 선택 다이얼로그 표시
  Future<void> _showDateRangePicker() async {
    await showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: AppColors.surface,
          surfaceTintColor: Colors.transparent,
          title: Text(
            '행사 기간 선택',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: SizedBox(
            width: 300,
            height: 350,
            child: SfDateRangePicker(
              onSelectionChanged: _onDateRangeSelectionChanged,
              selectionMode: DateRangePickerSelectionMode.range,
              initialSelectedRange: _startDate != null && _endDate != null
                  ? PickerDateRange(_startDate, _endDate)
                  : null,
              minDate: DateTime.now(),
              maxDate: DateTime.now().add(const Duration(days: 365)),
              backgroundColor: AppColors.surface,
              todayHighlightColor: AppColors.primarySeed,
              selectionColor: AppColors.primarySeed,
              startRangeSelectionColor: AppColors.primarySeed,
              endRangeSelectionColor: AppColors.primarySeed,
              rangeSelectionColor: AppColors.primarySeed.withValues(alpha: 0.3),
              selectionTextStyle: TextStyle(
                color: AppColors.onPrimary,
                fontWeight: FontWeight.w500,
              ),
              rangeTextStyle: TextStyle(
                color: AppColors.onSurface,
                fontWeight: FontWeight.w400,
              ),
              headerStyle: DateRangePickerHeaderStyle(
                backgroundColor: AppColors.surface,
                textStyle: TextStyle(
                  color: AppColors.onSurface,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              monthViewSettings: DateRangePickerMonthViewSettings(
                firstDayOfWeek: 1, // 월요일부터 시작
                dayFormat: 'EEE',
                viewHeaderStyle: DateRangePickerViewHeaderStyle(
                  backgroundColor: AppColors.surface,
                  textStyle: TextStyle(
                    color: AppColors.onSurfaceVariant,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              monthCellStyle: DateRangePickerMonthCellStyle(
                textStyle: TextStyle(
                  color: AppColors.onSurface,
                  fontSize: 14,
                ),
                todayTextStyle: TextStyle(
                  color: AppColors.primarySeed,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                disabledDatesTextStyle: TextStyle(
                  color: AppColors.onSurface.withValues(alpha: 0.4),
                  fontSize: 14,
                ),
                leadingDatesTextStyle: TextStyle(
                  color: AppColors.onSurface.withValues(alpha: 0.6),
                  fontSize: 14,
                ),
                trailingDatesTextStyle: TextStyle(
                  color: AppColors.onSurface.withValues(alpha: 0.6),
                  fontSize: 14,
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                '취소',
                style: TextStyle(color: AppColors.onSurfaceVariant),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primarySeed,
                foregroundColor: AppColors.onPrimary,
              ),
              child: const Text('확인'),
            ),
          ],
        );
      },
    );
  }

  /// 날짜 범위 선택 변경 처리
  void _onDateRangeSelectionChanged(DateRangePickerSelectionChangedArgs args) {
    if (args.value is PickerDateRange) {
      final PickerDateRange range = args.value;
      setState(() {
        _startDate = range.startDate;
        _endDate = range.endDate ?? range.startDate;
      });
    }
  }

  /// 저장 버튼
  Widget _buildSaveButton() {
    return OnboardingComponents.buildPrimaryButton(
      context: context,
      text: _isEditing ? '수정' : '생성',
      onPressed: _isLoading ? null : _saveEvent,
      isLoading: _isLoading,
      icon: _isEditing ? Icons.edit : Icons.add,
    );
  }

  /// 날짜 범위 텍스트 반환
  String _getDateRangeText() {
    if (_startDate == null || _endDate == null) {
      return '행사 기간을 선택하세요';
    }

    final startText = '${_startDate!.year}년 ${_startDate!.month}월 ${_startDate!.day}일';
    final endText = '${_endDate!.year}년 ${_endDate!.month}월 ${_endDate!.day}일';

    return '$startText ~ $endText';
  }







  /// 날짜 범위 변경 시 관련 데이터 확인 (자동 처리)
  Future<bool> _checkDateRangeChange(Event originalEvent, Event newEvent) async {
    try {
      // 날짜 범위가 축소되는지 확인
      final isStartDateLater = newEvent.startDate.isAfter(originalEvent.startDate);
      final isEndDateEarlier = newEvent.endDate.isBefore(originalEvent.endDate);

      if (!isStartDateLater && !isEndDateEarlier) {
        // 날짜 범위가 확장되거나 동일하면 문제없음
        return true;
      }

      // 범위를 벗어나는 데이터 조회
      final affectedData = await _getAffectedDataByDateRange(
        originalEvent.id!,
        newEvent.startDate,
        newEvent.endDate,
        originalEvent.startDate,
        originalEvent.endDate,
      );

      if (affectedData.isNotEmpty) {
        // 영향받는 데이터가 있으면 로그로 기록하고 자동으로 처리
        LoggerUtils.logInfo('행사 기간 변경으로 인한 데이터 자동 정리: $affectedData', tag: _tag);

        // 사용자에게 간단한 알림만 표시 (차단하지 않음)
        if (mounted) {
          ToastUtils.showMessage(context, '행사 기간이 변경되어 관련 데이터가 자동으로 정리됩니다.');
        }
      }

      // 항상 true를 반환하여 진행
      return true;
    } catch (e) {
      LoggerUtils.logError('날짜 범위 변경 확인 중 오류', tag: _tag, error: e);
      // 오류 발생 시 안전하게 진행
      return true;
    }
  }

  /// 날짜 범위 변경으로 영향받는 데이터 조회
  Future<Map<String, int>> _getAffectedDataByDateRange(
    int eventId,
    DateTime newStartDate,
    DateTime newEndDate,
    DateTime originalStartDate,
    DateTime originalEndDate,
  ) async {
    final affectedData = <String, int>{};

    try {
      // 목표 수익 데이터 확인
      final revenueGoalRepository = ref.read(revenueGoalRepositoryProvider);
      final allGoals = await revenueGoalRepository.getGoalsByEventId(eventId);

      int affectedGoals = 0;
      for (final goal in allGoals) {
        final goalDate = DateTime.parse(goal.date);
        if (goalDate.isBefore(newStartDate) || goalDate.isAfter(newEndDate)) {
          affectedGoals++;
        }
      }
      if (affectedGoals > 0) {
        affectedData['목표 수익'] = affectedGoals;
      }

      // 판매 기록 데이터 확인
      final salesLogRepository = ref.read(salesLogRepositoryProvider);

      // 새 범위 이전의 판매 기록
      if (newStartDate.isAfter(originalStartDate)) {
        final beforeRangeLogs = await salesLogRepository.getSalesLogsByDateRange(
          originalStartDate,
          newStartDate.subtract(const Duration(days: 1)),
        );
        final eventLogs = beforeRangeLogs.where((log) => log.eventId == eventId).toList();
        if (eventLogs.isNotEmpty) {
          affectedData['판매 기록 (시작일 이전)'] = eventLogs.length;
        }
      }

      // 새 범위 이후의 판매 기록
      if (newEndDate.isBefore(originalEndDate)) {
        final afterRangeLogs = await salesLogRepository.getSalesLogsByDateRange(
          newEndDate.add(const Duration(days: 1)),
          originalEndDate,
        );
        final eventLogs = afterRangeLogs.where((log) => log.eventId == eventId).toList();
        if (eventLogs.isNotEmpty) {
          affectedData['판매 기록 (종료일 이후)'] = eventLogs.length;
        }
      }

      // 선입금 데이터 확인
      final prepaymentRepository = ref.read(prepaymentRepositoryProvider);
      final allPrepayments = await prepaymentRepository.getPrepaymentsByEventId(eventId);

      int affectedPrepayments = 0;
      for (final prepayment in allPrepayments) {
        final prepaymentDate = DateTime.fromMillisecondsSinceEpoch(prepayment.registrationTimestamp);
        if (prepaymentDate.isBefore(newStartDate) || prepaymentDate.isAfter(newEndDate)) {
          affectedPrepayments++;
        }
      }
      if (affectedPrepayments > 0) {
        affectedData['선입금'] = affectedPrepayments;
      }

    } catch (e) {
      LoggerUtils.logError('영향받는 데이터 조회 중 오류', tag: _tag, error: e);
    }

    return affectedData;
  }



  /// 백그라운드에서 날짜 범위를 벗어나는 데이터 삭제
  void _deleteOutOfRangeDataInBackground(int eventId, DateTime newStartDate, DateTime newEndDate) {
    // 백그라운드에서 실행하여 UI 블로킹 방지
    Future.microtask(() async {
      try {
        LoggerUtils.logInfo('날짜 범위 외 데이터 삭제 시작: eventId $eventId', tag: _tag);

        // 1. 목표 수익 데이터 삭제
        await _deleteOutOfRangeRevenueGoals(eventId, newStartDate, newEndDate);

        // 2. 판매 기록 데이터 삭제
        await _deleteOutOfRangeSalesLogs(eventId, newStartDate, newEndDate);

        // 3. 선입금 데이터 삭제
        await _deleteOutOfRangePrepayments(eventId, newStartDate, newEndDate);

        // 4. 관련 Provider 상태 갱신
        await _refreshRelatedProviders();

        LoggerUtils.logInfo('날짜 범위 외 데이터 삭제 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('날짜 범위 외 데이터 삭제 실패', tag: _tag, error: e);
        // 삭제 실패 시 사용자에게 알림 (UI 스레드에서 실행)
        if (mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ToastUtils.showError(context, '일부 데이터 정리에 실패했습니다. 수동으로 확인해주세요.');
          });
        }
      }
    });
  }

  /// 날짜 범위를 벗어나는 목표 수익 삭제
  Future<void> _deleteOutOfRangeRevenueGoals(int eventId, DateTime newStartDate, DateTime newEndDate) async {
    try {
      final revenueGoalRepository = ref.read(revenueGoalRepositoryProvider);
      final allGoals = await revenueGoalRepository.getGoalsByEventId(eventId);

      final goalsToDelete = <String>[];
      for (final goal in allGoals) {
        final goalDate = DateTime.parse(goal.date);
        if (goalDate.isBefore(newStartDate) || goalDate.isAfter(newEndDate)) {
          if (goal.id != null) {
            goalsToDelete.add(goal.id!);
          }
        }
      }

      // 로컬 DB에서 삭제
      for (final goalId in goalsToDelete) {
        await revenueGoalRepository.deleteGoal(goalId);
      }

      // Firebase에서도 삭제
      if (goalsToDelete.isNotEmpty) {
        await _deleteRevenueGoalsFromFirebase(eventId, goalsToDelete);
      }

      LoggerUtils.logInfo('목표 수익 삭제 완료: ${goalsToDelete.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('목표 수익 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 날짜 범위를 벗어나는 판매 기록 삭제
  Future<void> _deleteOutOfRangeSalesLogs(int eventId, DateTime newStartDate, DateTime newEndDate) async {
    try {
      final salesLogRepository = ref.read(salesLogRepositoryProvider);

      // 시작일 이전 데이터 삭제
      if (newStartDate.isAfter(DateTime(1970))) {
        final beforeLogs = await salesLogRepository.getSalesLogsByDateRange(
          DateTime(1970),
          newStartDate.subtract(const Duration(days: 1)),
        );
        final eventLogs = beforeLogs.where((log) => log.eventId == eventId).toList();

        for (final log in eventLogs) {
          await salesLogRepository.deleteSalesLog(log.id);
        }

        if (eventLogs.isNotEmpty) {
          await _deleteSalesLogsFromFirebase(eventId, eventLogs.map((log) => log.id).toList());
        }
      }

      // 종료일 이후 데이터 삭제
      final afterLogs = await salesLogRepository.getSalesLogsByDateRange(
        newEndDate.add(const Duration(days: 1)),
        DateTime(2100),
      );
      final eventLogsAfter = afterLogs.where((log) => log.eventId == eventId).toList();

      for (final log in eventLogsAfter) {
        await salesLogRepository.deleteSalesLog(log.id);
      }

      if (eventLogsAfter.isNotEmpty) {
        await _deleteSalesLogsFromFirebase(eventId, eventLogsAfter.map((log) => log.id).toList());
      }

      LoggerUtils.logInfo('판매 기록 삭제 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매 기록 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 날짜 범위를 벗어나는 선입금 삭제
  Future<void> _deleteOutOfRangePrepayments(int eventId, DateTime newStartDate, DateTime newEndDate) async {
    try {
      final prepaymentRepository = ref.read(prepaymentRepositoryProvider);
      final allPrepayments = await prepaymentRepository.getPrepaymentsByEventId(eventId);

      final prepaymentsToDelete = <int>[];
      for (final prepayment in allPrepayments) {
        final prepaymentDate = DateTime.fromMillisecondsSinceEpoch(prepayment.registrationTimestamp);
        if (prepaymentDate.isBefore(newStartDate) || prepaymentDate.isAfter(newEndDate)) {
          prepaymentsToDelete.add(prepayment.id);
        }
      }

      // 로컬 DB에서 삭제
      for (final prepaymentId in prepaymentsToDelete) {
        await prepaymentRepository.deletePrepayment(prepaymentId);
      }

      // Firebase에서도 삭제
      if (prepaymentsToDelete.isNotEmpty) {
        await _deletePrepaymentsFromFirebase(eventId, prepaymentsToDelete);
      }

      LoggerUtils.logInfo('선입금 삭제 완료: ${prepaymentsToDelete.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// Firebase에서 목표 수익 삭제
  Future<void> _deleteRevenueGoalsFromFirebase(int eventId, List<String> goalIds) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        LoggerUtils.logWarning('사용자가 로그인되지 않아 Firebase 목표 수익 삭제를 건너뜁니다', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('Firebase 목표 수익 삭제 시작: ${goalIds.length}개', tag: _tag);

      final firestore = FirebaseFirestore.instance;
      final batch = firestore.batch();

      for (final goalId in goalIds) {
        final docRef = firestore
            .collection('users')
            .doc(user.uid)
            .collection('events')
            .doc(eventId.toString())
            .collection('revenue_goals')
            .doc(goalId);

        batch.delete(docRef);
      }

      await batch.commit();
      LoggerUtils.logInfo('Firebase 목표 수익 삭제 완료: ${goalIds.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Firebase 목표 수익 삭제 실패', tag: _tag, error: e);
      // Firebase 삭제 실패해도 로컬 삭제는 유지
    }
  }

  /// Firebase에서 판매 기록 삭제
  Future<void> _deleteSalesLogsFromFirebase(int eventId, List<int> logIds) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        LoggerUtils.logWarning('사용자가 로그인되지 않아 Firebase 판매 기록 삭제를 건너뜁니다', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('Firebase 판매 기록 삭제 시작: ${logIds.length}개', tag: _tag);

      final firestore = FirebaseFirestore.instance;
      final batch = firestore.batch();

      for (final logId in logIds) {
        final docRef = firestore
            .collection('users')
            .doc(user.uid)
            .collection('events')
            .doc(eventId.toString())
            .collection('sales_logs')
            .doc(logId.toString());

        batch.delete(docRef);
      }

      await batch.commit();
      LoggerUtils.logInfo('Firebase 판매 기록 삭제 완료: ${logIds.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Firebase 판매 기록 삭제 실패', tag: _tag, error: e);
      // Firebase 삭제 실패해도 로컬 삭제는 유지
    }
  }

  /// Firebase에서 선입금 삭제
  Future<void> _deletePrepaymentsFromFirebase(int eventId, List<int> prepaymentIds) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        LoggerUtils.logWarning('사용자가 로그인되지 않아 Firebase 선입금 삭제를 건너뜁니다', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('Firebase 선입금 삭제 시작: ${prepaymentIds.length}개', tag: _tag);

      final firestore = FirebaseFirestore.instance;
      final batch = firestore.batch();

      for (final prepaymentId in prepaymentIds) {
        final docRef = firestore
            .collection('users')
            .doc(user.uid)
            .collection('events')
            .doc(eventId.toString())
            .collection('prepayments')
            .doc(prepaymentId.toString());

        batch.delete(docRef);
      }

      await batch.commit();
      LoggerUtils.logInfo('Firebase 선입금 삭제 완료: ${prepaymentIds.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Firebase 선입금 삭제 실패', tag: _tag, error: e);
      // Firebase 삭제 실패해도 로컬 삭제는 유지
    }
  }

  /// 행사 수정 완료 후 강제 갱신 (홈 화면 즉시 반영)
  Future<void> _forceRefreshAfterEventUpdate() async {
    try {
      LoggerUtils.logInfo('행사 수정 완료 후 강제 갱신 시작', tag: _tag);

      // 1. 모든 핵심 Provider들 완전 초기화
      ref.invalidate(currentWorkspaceProvider);
      ref.invalidate(homeDashboardDateRangeProvider);
      ref.invalidate(homeDashboardSellerFilterProvider);
      ref.invalidate(salesStatsProvider);
      ref.invalidate(revenueGoalStatsProvider);

      // 2. 이벤트 목록 갱신
      await ref.read(eventNotifierProvider.notifier).loadEvents();

      // 3. 모든 데이터 Provider들 강제 새로고침
      await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
      await ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);
      await ref.read(revenueGoalNotifierProvider.notifier).loadGoals(showLoading: false);
      await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();
      await ref.read(sellerNotifierProvider.notifier).loadSellers();

      // 4. 충분한 지연으로 모든 갱신 완료 보장
      await Future.delayed(const Duration(milliseconds: 200));

      LoggerUtils.logInfo('행사 수정 완료 후 강제 갱신 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('행사 수정 완료 후 강제 갱신 실패', tag: _tag, error: e);
    }
  }

  /// 관련 Provider들의 상태 갱신 (전체 앱 연동)
  Future<void> _refreshRelatedProviders() async {
    try {
      // 1. 목표 수익 Provider 갱신
      await ref.read(revenueGoalNotifierProvider.notifier).loadGoals(showLoading: false);

      // 2. 판매 기록 Provider 갱신
      await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();

      // 3. 선입금 Provider 갱신
      await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();

      // 4. 통계 관련 Provider 갱신 (강제 새로고침)
      ref.invalidate(salesStatsProvider);

      // 5. 현재 워크스페이스 갱신 (행사 정보 반영)
      ref.invalidate(currentWorkspaceProvider);

      // 6. 홈 대시보드 관련 Provider들 갱신
      ref.invalidate(revenueGoalStatsProvider);

      // 7. 관련 Notifier들 직접 갱신 (즉시 반영)
      try {
        await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
        await ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);
        await ref.read(revenueGoalNotifierProvider.notifier).loadGoals(showLoading: false);
        LoggerUtils.logInfo('행사 기간 변경 후 관련 Provider 갱신 완료', tag: 'EventFormScreen');
      } catch (e) {
        LoggerUtils.logError('관련 Provider 갱신 실패', tag: 'EventFormScreen', error: e);
      }

      // 7. 이벤트 목록 갱신
      await ref.read(eventNotifierProvider.notifier).loadEvents();

      LoggerUtils.logInfo('관련 Provider 상태 갱신 완료 (전체 앱 연동)', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('관련 Provider 상태 갱신 실패', tag: _tag, error: e);
    }
  }

  /// 변경된 필드만 선택적으로 업데이트
  Future<void> _updateEventSelectively(Event newEvent) async {
    final originalEvent = widget.event!;
    final changedFields = <String, dynamic>{};

    // 각 필드별로 변경사항 확인
    if (originalEvent.name != newEvent.name) {
      changedFields['name'] = newEvent.name;
    }

    if (originalEvent.description != newEvent.description) {
      changedFields['description'] = newEvent.description;
    }

    if (originalEvent.imagePath != newEvent.imagePath) {
      changedFields['imagePath'] = newEvent.imagePath;
    }

    if (!originalEvent.startDate.isAtSameMomentAs(newEvent.startDate)) {
      changedFields['startDate'] = newEvent.startDate;
    }

    if (!originalEvent.endDate.isAtSameMomentAs(newEvent.endDate)) {
      changedFields['endDate'] = newEvent.endDate;
    }

    if (originalEvent.isActive != newEvent.isActive) {
      changedFields['isActive'] = newEvent.isActive;
    }

    // 변경사항이 있는 경우에만 업데이트
    if (changedFields.isNotEmpty) {
      // 날짜 변경이 있는 경우 관련 데이터 처리 확인
      if (changedFields.containsKey('startDate') || changedFields.containsKey('endDate')) {
        final shouldProceed = await _checkDateRangeChange(originalEvent, newEvent);
        if (!shouldProceed) return;
      }

      // Event Provider의 updateEventFields 사용 (이미 구현된 선택적 업데이트)
      await ref.read(eventNotifierProvider.notifier).updateEventFields(originalEvent.id!, changedFields);

      // 행사 업데이트 성공 후 백그라운드에서 관련 데이터 삭제
      if (changedFields.containsKey('startDate') || changedFields.containsKey('endDate')) {
        _deleteOutOfRangeDataInBackground(originalEvent.id!, newEvent.startDate, newEvent.endDate);
      }
    }
  }

  /// 행사 저장
  Future<void> _saveEvent() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_startDate == null || _endDate == null) {
      ToastUtils.showMessage(context, '행사 기간을 선택해주세요');
      return;
    }



    setState(() {
      _isLoading = true;
    });

    try {
      LoggerUtils.methodStart('_saveEvent', tag: _tag);

      final event = Event.create(
        id: _isEditing ? widget.event!.id : null,
        name: _nameController.text.trim(),
        imagePath: _imagePath,
        startDate: _startDate!,
        endDate: _endDate!,
        isActive: true, // 기본값으로 설정
        description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      );

      if (_isEditing) {
        // 변경된 필드만 업데이트
        await _updateEventSelectively(event);
      } else {
        final addedEvent = await ref.read(eventNotifierProvider.notifier).addEvent(event);

        // 새 행사 생성 후 자동으로 현재 행사로 설정하여 닉네임 기반 판매자 자동 생성 보장
        if (addedEvent != null) {
          LoggerUtils.logInfo('새 행사 생성 완료, 워크스페이스 전환 시작: ${addedEvent.name} (ID: ${addedEvent.id})', tag: _tag);
          try {
            final workspace = EventWorkspaceUtils.eventToWorkspace(addedEvent);
            if (workspace != null) {
              LoggerUtils.logInfo('EventWorkspace 변환 완료: ${workspace.name} (ID: ${workspace.id})', tag: _tag);
              await ref.read(unifiedWorkspaceProvider.notifier).switchToWorkspace(workspace);
              LoggerUtils.logInfo('워크스페이스 전환 완료', tag: _tag);
            } else {
              LoggerUtils.logError('EventWorkspace 변환 실패', tag: _tag);
            }
          } catch (workspaceError) {
            LoggerUtils.logError('워크스페이스 전환 중 오류 발생', tag: _tag, error: workspaceError);
            // 워크스페이스 전환 실패해도 행사 생성은 성공했으므로 계속 진행
          }
        } else {
          LoggerUtils.logError('새 행사 생성 실패', tag: _tag);
        }
      }

      if (mounted) {
        // 수정 완료 후 홈 화면으로 완전히 새로 이동 (확실한 UI 갱신)
        if (_isEditing) {
          await _forceRefreshAfterEventUpdate();
          // Navigator.pop() 대신 홈 화면으로 완전히 새로 이동
          Navigator.of(context).pushNamedAndRemoveUntil(
            '/',
            (route) => false,
          );
        } else {
          // 새 행사 생성 시 모든 이전 화면을 제거하고 메인 화면으로 이동
          Navigator.of(context).pushNamedAndRemoveUntil(
            '/',
            (route) => false,
          );
        }

        // 성공 메시지는 부모 화면에서 처리하도록 변경
      }

      LoggerUtils.methodEnd('_saveEvent', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 저장 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );

      if (mounted) {
        // 에러 메시지는 부모 화면에서 처리하도록 변경
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
