import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:logger/logger.dart';
import '../utils/app_version_utils.dart';

/// Firebase Remote Config 서비스
/// 앱 버전 체크, 플러스 플랜 무료 기간 등을 서버에서 관리
class RemoteConfigService {
  static final Logger _logger = Logger();
  static const String _tag = 'RemoteConfigService';
  
  static FirebaseRemoteConfig? _remoteConfig;
  static bool _initialized = false;
  
  // Remote Config 키들
  static const String _minSubscriptionVersionKey = 'min_subscription_version';
  static const String _plusPlanFreeUntilKey = 'plus_plan_free_until';
  static const String _subscriptionBlockedMessageKey = 'subscription_blocked_message';
  static const String _plusPlanEventMessageKey = 'plus_plan_event_message';
  
  /// Remote Config 초기화
  static Future<void> initialize() async {
    try {
      _remoteConfig = FirebaseRemoteConfig.instance;
      
      // 기본값 설정
      await _remoteConfig!.setDefaults({
        _minSubscriptionVersionKey: '1.0.9', // 구독에 필요한 최소 버전
        _plusPlanFreeUntilKey: '2024-12-31', // 플러스 플랜 무료 종료일
        _subscriptionBlockedMessageKey: '구독하려면 앱을 최신 버전으로 업데이트해주세요.',
        _plusPlanEventMessageKey: '오픈 이벤트 기간 중 무료!',
      });
      
      // 설정
      await _remoteConfig!.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 10),
        minimumFetchInterval: const Duration(minutes: 1), // 개발용, 실제로는 더 길게
      ));
      
      // 초기 fetch
      await _fetchAndActivate();
      
      _initialized = true;
      _logger.i('[$_tag] Remote Config 초기화 완료');
    } catch (e) {
      _logger.e('[$_tag] Remote Config 초기화 실패: $e');
      _initialized = false;
    }
  }
  
  /// Remote Config 값 fetch 및 활성화
  static Future<void> _fetchAndActivate() async {
    try {
      await _remoteConfig!.fetchAndActivate();
      _logger.i('[$_tag] Remote Config fetch 및 활성화 완료');
    } catch (e) {
      _logger.e('[$_tag] Remote Config fetch 실패: $e');
    }
  }
  
  /// 구독 가능 여부 체크 (버전 확인)
  static Future<SubscriptionEligibilityResult> checkSubscriptionEligibility() async {
    try {
      if (!_initialized || _remoteConfig == null) {
        await initialize();
      }
      
      // 최신 값 fetch (캐시된 값이 아닌)
      await _fetchAndActivate();
      
      final minVersion = _remoteConfig!.getString(_minSubscriptionVersionKey);
      final currentVersion = AppVersionUtils.currentVersion;
      
      _logger.i('[$_tag] 버전 체크: 현재=$currentVersion, 최소=$minVersion');
      
      if (AppVersionUtils.isVersionLowerThan(minVersion)) {
        final message = _remoteConfig!.getString(_subscriptionBlockedMessageKey);
        return SubscriptionEligibilityResult.blocked(
          message: message,
          currentVersion: currentVersion,
          requiredVersion: minVersion,
        );
      }
      
      return SubscriptionEligibilityResult.eligible();
    } catch (e) {
      _logger.e('[$_tag] 구독 자격 체크 실패: $e');
      // 에러 시 안전하게 허용 (네트워크 문제 등)
      return SubscriptionEligibilityResult.eligible();
    }
  }
  
  /// 플러스 플랜 무료 기간 여부 체크
  static Future<bool> isPlusPlanFreeNow() async {
    try {
      if (!_initialized || _remoteConfig == null) {
        await initialize();
      }
      
      await _fetchAndActivate();
      
      final freeUntilStr = _remoteConfig!.getString(_plusPlanFreeUntilKey);
      final freeUntil = DateTime.tryParse(freeUntilStr);
      
      if (freeUntil == null) {
        _logger.w('[$_tag] 플러스 플랜 무료 종료일 파싱 실패: $freeUntilStr');
        return false;
      }
      
      final now = DateTime.now();
      final isFree = now.isBefore(freeUntil);
      
      _logger.i('[$_tag] 플러스 플랜 무료 여부: $isFree (종료일: $freeUntil)');
      return isFree;
    } catch (e) {
      _logger.e('[$_tag] 플러스 플랜 무료 기간 체크 실패: $e');
      return false; // 에러 시 유료로 처리
    }
  }
  
  /// 플러스 플랜 이벤트 메시지 가져오기
  static Future<String> getPlusPlanEventMessage() async {
    try {
      if (!_initialized || _remoteConfig == null) {
        await initialize();
      }
      
      await _fetchAndActivate();
      return _remoteConfig!.getString(_plusPlanEventMessageKey);
    } catch (e) {
      _logger.e('[$_tag] 플러스 플랜 이벤트 메시지 가져오기 실패: $e');
      return '오픈 이벤트 기간 중 무료!';
    }
  }
  
  /// 플러스 플랜 무료 종료일 가져오기
  static Future<DateTime?> getPlusPlanFreeEndDate() async {
    try {
      if (!_initialized || _remoteConfig == null) {
        await initialize();
      }
      
      await _fetchAndActivate();
      
      final freeUntilStr = _remoteConfig!.getString(_plusPlanFreeUntilKey);
      return DateTime.tryParse(freeUntilStr);
    } catch (e) {
      _logger.e('[$_tag] 플러스 플랜 무료 종료일 가져오기 실패: $e');
      return null;
    }
  }
  
  /// Remote Config 강제 새로고침
  static Future<void> forceRefresh() async {
    try {
      if (_remoteConfig != null) {
        await _fetchAndActivate();
        _logger.i('[$_tag] Remote Config 강제 새로고침 완료');
      }
    } catch (e) {
      _logger.e('[$_tag] Remote Config 강제 새로고침 실패: $e');
    }
  }
}

/// 구독 자격 체크 결과
class SubscriptionEligibilityResult {
  final bool isEligible;
  final String? message;
  final String? currentVersion;
  final String? requiredVersion;
  
  const SubscriptionEligibilityResult._({
    required this.isEligible,
    this.message,
    this.currentVersion,
    this.requiredVersion,
  });
  
  factory SubscriptionEligibilityResult.eligible() {
    return const SubscriptionEligibilityResult._(isEligible: true);
  }
  
  factory SubscriptionEligibilityResult.blocked({
    required String message,
    required String currentVersion,
    required String requiredVersion,
  }) {
    return SubscriptionEligibilityResult._(
      isEligible: false,
      message: message,
      currentVersion: currentVersion,
      requiredVersion: requiredVersion,
    );
  }
}
