import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/product.dart';
import '../../models/sale.dart';
import '../../models/category.dart';
import '../../utils/currency_utils.dart';
import '../../widgets/product_image.dart';
import '../../utils/responsive_text_utils.dart';
import '../../utils/device_utils.dart';
import '../../utils/app_colors.dart';
import '../../providers/category_provider.dart';

/// 판매 화면의 UI 컴포넌트들을 담당하는 클래스
///
/// 주요 기능:
/// - 상품 그리드 아이템
/// - 할인 그리드 아이템
/// - 반응형 디자인 유틸리티
/// - 현대적 Material Design 3 스타일 적용
class SaleUiComponents {
  /// 반응형 열 수 계산 (기본값만 제공, 실제로는 설정값 사용 권장)
  /// 이 메서드는 설정값이 없을 때의 기본값만 제공합니다.
  static int getCrossAxisCount(BuildContext context) {
    return DeviceUtils.getOptimalGridColumns(context);
  }

  /// 반응형 카드 마진
  static double getCardMargin(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) return 2.0; // 태블릿
    return 1.0; // 모바일
  }

  /// 반응형 카드 elevation
  static double getCardElevation(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) return 4.0; // 태블릿
    return 2.0; // 모바일
  }

  /// 반응형 카드 corner radius
  static double getCardCornerRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) return 8.0; // 태블릿
    return 6.0; // 모바일
  }

  /// 반응형 텍스트 마진
  static EdgeInsets getTextMargin(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) {
      return const EdgeInsets.symmetric(horizontal: 8, vertical: 4); // 태블릿
    }
    return const EdgeInsets.symmetric(horizontal: 6, vertical: 3); // 모바일
  }

  /// 반응형 수량 텍스트 패딩
  static EdgeInsets getQuantityTextPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) {
      return const EdgeInsets.symmetric(horizontal: 12, vertical: 6); // 태블릿
    }
    return const EdgeInsets.symmetric(horizontal: 8, vertical: 4); // 모바일
  }

  /// 반응형 상품명 텍스트 스타일
  static TextStyle getProductNameTextStyle(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) {
      return Theme.of(context).textTheme.titleMedium!.copyWith(
        fontFamily: 'Pretendard',
        fontSize: 14,
        fontWeight: FontWeight.bold,
        inherit: true,
      ); // 태블릿
    }
    return Theme.of(context).textTheme.titleSmall!.copyWith(
      fontFamily: 'Pretendard',
      fontSize: 12,
      fontWeight: FontWeight.bold,
      inherit: true,
    ); // 모바일
  }

  /// 반응형 가격 텍스트 스타일
  static TextStyle getPriceTextStyle(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) {
      return Theme.of(context).textTheme.bodyMedium!.copyWith(
        fontFamily: 'Pretendard',
        fontSize: 12,
        color: AppColors.onboardingPrimary,
        inherit: true,
      ); // 태블릿
    }
    return Theme.of(context).textTheme.bodySmall!.copyWith(
      fontFamily: 'Pretendard',
      fontSize: 10,
      color: AppColors.onboardingPrimary,
      inherit: true,
    ); // 모바일
  }

  /// 반응형 수량 텍스트 스타일
  static TextStyle getQuantityTextStyle(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) {
      return Theme.of(context).textTheme.titleSmall!.copyWith(
        fontFamily: 'Pretendard',
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.white,
        inherit: true,
      ); // 태블릿
    }
    return Theme.of(context).textTheme.titleSmall!.copyWith(
      fontFamily: 'Pretendard',
      fontSize: 14,
      fontWeight: FontWeight.bold,
      color: Colors.white,
      inherit: true,
    ); // 모바일
  }

  /// 상품 그리드 아이템 위젯
  static Widget buildProductGridItem({
    Key? key,
    required Product product,
    required bool isSelected,
    required int quantity,
    required VoidCallback? onTap,
    required VoidCallback? onLongPress,
    required VoidCallback? onDecrease, // - 버튼 콜백 추가
    required int columns,
    bool isEditMode = false, // 편집 모드 여부
    bool isDeletionMode = false, // 삭제 모드 여부
    bool isCheckedForDeletion = false, // 삭제용 체크 상태
    VoidCallback? onCheckToggle, // 체크 토글 콜백
    bool showImages = true, // 이미지 표시 여부
  }) {
    // 이미지 표시 여부에 따라 다른 그리드 아이템 사용
    if (showImages) {
      return _ProductGridItem(
        key: key,
        product: product,
        isSelected: isSelected,
        quantity: quantity,
        onTap: onTap,
        onLongPress: onLongPress,
        onDecrease: onDecrease,
        columns: columns,
        isEditMode: isEditMode,
        isDeletionMode: isDeletionMode,
        isCheckedForDeletion: isCheckedForDeletion,
        onCheckToggle: onCheckToggle,
      );
    } else {
      return _TextOnlyProductGridItem(
        key: key,
        product: product,
        isSelected: isSelected,
        quantity: quantity,
        onTap: onTap,
        onLongPress: onLongPress,
        onDecrease: onDecrease,
        columns: columns,
        isEditMode: isEditMode,
        isDeletionMode: isDeletionMode,
        isCheckedForDeletion: isCheckedForDeletion,
        onCheckToggle: onCheckToggle,
      );
    }
  }

  /// 할인 그리드 아이템 위젯
  static Widget buildSaleGridItem({
    required Sale sale,
    required bool isSelected,
    required int quantity,
    required VoidCallback? onTap,
    required VoidCallback? onLongPress,
  }) {
    return _SaleGridItem(
      sale: sale,
      isSelected: isSelected,
      quantity: quantity,
      onTap: onTap,
      onLongPress: onLongPress,
    );
  }

  /// 총 합계 표시 위젯
  static Widget buildTotalAmountWidget({
    required BuildContext context,
    required int totalAmount,
    required VoidCallback? onSellPressed,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 총 합계 라벨과 금액
          Row(
            children: [
              Text('총 합계:', style: Theme.of(context).textTheme.titleMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 18)),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  CurrencyUtils.formatCurrency(totalAmount),
                  textAlign: TextAlign.end,
                  style: Theme.of(context).textTheme.titleMedium!.copyWith(
                    fontFamily: 'Pretendard',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // 판매 버튼
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: totalAmount > 0 ? onSellPressed : null,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('선택 상품 판매'),
            ),
          ),
        ],
      ),
    );
  }
}

/// 상품 그리드 아이템 위젯 - 깜빡거림 방지를 위한 StatefulWidget
class _ProductGridItem extends StatefulWidget {
  final Product product;
  final bool isSelected;
  final int quantity;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onDecrease; // - 버튼 콜백 추가
  final int columns; // 열 수 추가
  final bool isEditMode; // 편집 모드 여부
  final bool isDeletionMode; // 삭제 모드 여부
  final bool isCheckedForDeletion; // 삭제용 체크 상태
  final VoidCallback? onCheckToggle; // 체크 토글 콜백
  const _ProductGridItem({
    super.key,
    required this.product,
    required this.isSelected,
    required this.quantity,
    this.onTap,
    this.onLongPress,
    this.onDecrease,
    required this.columns,
    this.isEditMode = false,
    this.isDeletionMode = false,
    this.isCheckedForDeletion = false,
    this.onCheckToggle,
  });

  @override
  State<_ProductGridItem> createState() => _ProductGridItemState();
}

class _ProductGridItemState extends State<_ProductGridItem> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true; // 위젯이 dispose되지 않도록 유지

  /// 위치가 조정된 재고 수량 배지 위젯 생성
  Widget _buildPositionedQuantityBadge(BuildContext context) {
    final quantityText = widget.product.quantity > 99
        ? '99+'
        : '${widget.product.quantity}';

    // 텍스트 길이에 따라 동적으로 크기 조정 (99+는 3글자이므로 확장 필요)
    final isLongText = quantityText.length > 2;
    final baseSize = DeviceUtils.getQuantityBadgeSize(context, widget.columns);
    final dynamicWidth = isLongText
        ? baseSize * 1.5  // 99+일 때 가로 크기 50% 증가 (더 넉넉하게)
        : baseSize;

    final position = DeviceUtils.getQuantityBadgePosition(context, widget.columns);

    return Positioned(
      right: position.right,
      bottom: position.bottom,
      child: Container(
        constraints: BoxConstraints(
          minWidth: baseSize,
          minHeight: baseSize,
          maxWidth: baseSize * 1.6, // 최대 60% 증가까지 허용 (99+에 충분한 공간)
        ),
        width: dynamicWidth,
        height: baseSize,
        decoration: BoxDecoration(
          gradient: widget.product.isOutOfStock
            ? LinearGradient(
                colors: [
                  AppColors.error.withValues(alpha: 0.8),
                  AppColors.errorLight.withValues(alpha: 0.6),
                ],
              )
            : LinearGradient(
                colors: [
                  AppColors.onboardingPrimary.withValues(alpha: 0.9), // 주황색 계열
                  AppColors.onboardingPrimaryDark.withValues(alpha: 0.9), // 진한 주황색
                ],
              ),
          borderRadius: BorderRadius.circular(
            DeviceUtils.getQuantityBadgeBorderRadius(context, widget.columns), // 라운드 사각형
          ),
          border: Border.all(
            color: AppColors.surface,
            width: 1.5, // 2.0에서 1.5로 테두리 굵기 감소
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            quantityText,
            style: TextStyle(
              color: AppColors.onboardingTextOnPrimary,
              fontSize: DeviceUtils.getQuantityBadgeTextSize(context, widget.columns),
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // AutomaticKeepAliveClientMixin 필수 호출

    final isSmartphone = DeviceUtils.isSmartphone(context);
    final isTablet = !isSmartphone;
    final borderRadius = isTablet ? 16.0 : 12.0;

    return Container(
      margin: DeviceUtils.getCardMargin(context, widget.columns),
      decoration: BoxDecoration(
        gradient: widget.product.isOutOfStock
          ? LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.secondary.withValues(alpha: 0.3),
                AppColors.secondaryDark.withValues(alpha: 0.3),
              ],
            )
          : AppColors.cardGradient,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(borderRadius),
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: widget.onTap,
          onLongPress: widget.onLongPress,
          child: Padding(
            padding: EdgeInsets.only(
              left: DeviceUtils.getCardPadding(context, widget.columns).left,
              right: DeviceUtils.getCardPadding(context, widget.columns).right,
              top: DeviceUtils.getCardPadding(context, widget.columns).top,
              bottom: 0.2, // 하단 패딩 더욱 최소화 - 텍스트 아래 공간 축소
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.max, // 카드 전체 높이 사용
              mainAxisAlignment: MainAxisAlignment.start, // 위에서부터 배치
              children: [
                // 이미지 영역 - 가로 공간 100% 활용하는 정사각형
                AspectRatio(
                  aspectRatio: 1.0, // 완벽한 정사각형
                  child: Stack(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppColors.surfaceVariant,
                              AppColors.secondary.withValues(alpha: 0.5),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(borderRadius * 0.7),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(borderRadius * 0.7),
                          child: ProductImage(
                                imagePath: widget.product.imagePath?.isNotEmpty == true
                                    ? widget.product.imagePath!
                                    : '', // 빈 문자열을 전달하면 ProductImage가 기본 이미지를 표시
                                fit: BoxFit.cover, // 크롭된 이미지 표시
                              ),
                        ),
                      ),

                      // 선택 오버레이 (이미지에만 적용)
                      if (widget.isSelected && !widget.product.isOutOfStock)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.grey.withValues(alpha: 0.4), // 회색으로 변경
                                  Colors.grey.shade600.withValues(alpha: 0.3), // 진한 회색으로 변경
                                ],
                              ),
                              borderRadius: BorderRadius.circular(borderRadius * 0.7),
                            ),
                          ),
                        ),

                      // 품절 오버레이
                      if (widget.product.isOutOfStock)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppColors.error.withValues(alpha: 0.8),
                                  AppColors.errorLight.withValues(alpha: 0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(borderRadius * 0.7),
                            ),
                            child: Center(
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: isTablet ? 16.0 : 12.0,
                                  vertical: isTablet ? 8.0 : 6.0,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.onboardingDarkBrown.withValues(alpha: 0.9),
                                  borderRadius: BorderRadius.circular(borderRadius * 0.5),
                                ),
                                child: Text(
                                  '품절',
                                  style: Theme.of(context).textTheme.titleMedium!.copyWith(
                                    fontFamily: 'Pretendard',
                                    color: AppColors.onboardingTextOnDark,
                                    fontSize: isTablet ? 20.0 : 16.0,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                      // 선택 오버레이 및 수량 표시
                      if (widget.isSelected)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppColors.neutral40.withValues(alpha: 0.6),
                                  AppColors.neutral30.withValues(alpha: 0.4),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(borderRadius * 0.7),
                            ),
                            child: Center(
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: isTablet ? 20.0 : 16.0,
                                  vertical: isTablet ? 12.0 : 8.0,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.neutral60, // 조금 더 진한 회색으로 변경
                                  borderRadius: BorderRadius.circular(borderRadius * 0.5),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.neutral60.withValues(alpha: 0.4),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: Text(
                                  widget.quantity.toString(),
                                  style: TextStyle(
                                    color: AppColors.onboardingTextOnPrimary,
                                    fontSize: isTablet ? 24.0 : 20.0,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                      // 재고 수량 배지 (우측 하단) - 항상 표시
                      if (widget.product.quantity > 0)
                        _buildPositionedQuantityBadge(context),

                      // 삭제 모드일 때 체크박스 (좌측 상단)
                      if (widget.isDeletionMode)
                        Positioned(
                          top: 4,
                          left: 4,
                          child: GestureDetector(
                            onTap: () => widget.onCheckToggle?.call(),
                            child: Container(
                              width: isTablet ? 28 : 24,
                              height: isTablet ? 28 : 24,
                              decoration: BoxDecoration(
                                color: widget.isCheckedForDeletion
                                  ? AppColors.onboardingPrimary
                                  : Colors.white.withValues(alpha: 0.9),
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: widget.isCheckedForDeletion
                                    ? AppColors.onboardingPrimary
                                    : Colors.grey.shade400,
                                  width: 2,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.25),
                                    blurRadius: 3,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                              child: Icon(
                                widget.isCheckedForDeletion ? Icons.check : null,
                                color: Colors.white,
                                size: isTablet ? 16 : 14,
                              ),
                            ),
                          ),
                        ),

                    ],
                  ),
                ),



                // 가격 - 상품명 바로 아래로 이동
                Container(
                  width: double.infinity,
                  margin: EdgeInsets.only(
                    top: DeviceUtils.getImageTextSpacing(context, widget.columns) * 0.3, // 상품명과 가격 간격 줄임
                  ),
                  padding: DeviceUtils.getPriceQuantityPadding(context, widget.columns),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(0xFFA0D8A0).withValues(alpha: 0.1), // 연한 초록색
                        const Color(0xFF8FCC8F).withValues(alpha: 0.1), // 조금 더 진한 연한 초록색
                      ],
                    ),
                    borderRadius: BorderRadius.circular(borderRadius * 0.3),
                  ),
                  child: Text(
                    CurrencyUtils.formatCurrency(widget.product.price) + '원',
                    style: TextStyle(
                      fontSize: DeviceUtils.getUnifiedTextSize(context, widget.columns),
                      color: const Color(0xFF4CAF50), // 원래 초록색으로 복원
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.center, // 중앙 정렬 복원
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                SizedBox(height: DeviceUtils.getComponentSpacing(context, widget.columns)), // 추가 간격 제거하여 더욱 컴팩트하게

                // 상품명 - 가격 아래로 이동 (2줄까지 최적화된 높이) - Expanded로 감싸서 남은 공간 활용
                Expanded(
                  child: Container(
                    margin: EdgeInsets.only(
                      left: ResponsiveTextUtils.getTextPadding(widget.columns).left,
                      right: ResponsiveTextUtils.getTextPadding(widget.columns).right,
                      // bottom margin 제거하여 하단 여백 축소
                    ),
                    child: Align(
                      alignment: Alignment(0.0, -0.7), // 상단에서 살짝 아래로 텍스트 배치
                      child: Text(
                        widget.product.name,
                        maxLines: 2, // 2줄 고정
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center, // 중앙 정렬
                        style: TextStyle(
                          fontSize: DeviceUtils.getUnifiedTextSize(context, widget.columns), // 통합 텍스트 크기 사용
                          color: widget.product.isOutOfStock
                            ? AppColors.onboardingTextSecondary
                            : widget.isSelected
                              ? AppColors.onboardingPrimary
                              : AppColors.onboardingTextPrimary,
                          fontWeight: FontWeight.w600,
                          height: 1.2, // 적절한 줄간격으로 되돌림 (가독성 확보)
                          fontFamily: 'Pretendard',
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 할인 그리드 아이템 위젯
class _SaleGridItem extends StatelessWidget {
  final Sale sale;
  final bool isSelected;
  final int quantity;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const _SaleGridItem({
    required this.sale,
    required this.isSelected,
    required this.quantity,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    final borderRadius = isTablet ? 16.0 : 12.0;

    return Container(
      margin: EdgeInsets.all(isTablet ? 6.0 : 4.0),
      decoration: BoxDecoration(
        gradient: isSelected
          ? LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.onboardingAccent.withValues(alpha: 0.3),
                AppColors.onboardingAccentLight.withValues(alpha: 0.2),
              ],
            )
          : LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.onboardingAccent.withValues(alpha: 0.1),
                AppColors.onboardingAccentLight.withValues(alpha: 0.05),
              ],
            ),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(borderRadius),
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: onTap,
          onLongPress: onLongPress,
          child: Padding(
            padding: EdgeInsets.all(isTablet ? 12.0 : 8.0),
            child: Column(
              children: [
                // 이미지 영역
                Expanded(
                  child: Stack(
                    children: [
                      // 이미지 컨테이너 - 정사각형 유지
                      AspectRatio(
                        aspectRatio: 1.0, // 정사각형 비율 강제
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: AppColors.accentGradient,
                            borderRadius: BorderRadius.circular(borderRadius * 0.7),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(borderRadius * 0.7),
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: AppColors.accentGradient,
                              ),
                              child: Icon(
                                Icons.local_offer_rounded,
                                size: isTablet ? 56.0 : 48.0,
                                color: AppColors.onboardingTextOnPrimary,
                              ),
                            ),
                          ),
                        ),
                      ),

                      // 선택 오버레이 및 수량 표시
                      if (isSelected)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppColors.neutral50.withValues(alpha: 0.4), // 0.8 → 0.4로 줄임
                                  AppColors.neutral60.withValues(alpha: 0.3), // 0.6 → 0.3으로 줄임
                                ],
                              ),
                              borderRadius: BorderRadius.circular(borderRadius * 0.7),
                            ),
                            child: Center(
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: isTablet ? 20.0 : 16.0,
                                  vertical: isTablet ? 12.0 : 8.0,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.neutral60, // 조금 더 진한 회색으로 변경
                                  borderRadius: BorderRadius.circular(borderRadius * 0.5),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.neutral60.withValues(alpha: 0.4),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: Text(
                                  quantity.toString(),
                                  style: TextStyle(
                                    color: AppColors.onboardingTextOnPrimary,
                                    fontSize: isTablet ? 24.0 : 20.0,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // 할인명
                Container(
                  margin: EdgeInsets.only(top: isTablet ? 12.0 : 8.0),
                  child: Text(
                    sale.name ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: isTablet ? 18.0 : 16.0,
                      fontWeight: FontWeight.bold,
                      color: isSelected
                        ? AppColors.onboardingAccent
                        : AppColors.onboardingTextPrimary,
                    ),
                  ),
                ),

                SizedBox(height: isTablet ? 8.0 : 4.0),

                // 할인 금액 - 이미지 영역과 동일한 너비로 제한
                LayoutBuilder(
                  builder: (context, constraints) {
                    final imageWidth = constraints.maxWidth;
                    return SizedBox(
                      width: imageWidth,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: isTablet ? 12.0 : 10.0,
                          vertical: isTablet ? 6.0 : 4.0,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppColors.onboardingAccent.withValues(alpha: 0.2),
                              AppColors.onboardingAccentLight.withValues(alpha: 0.1),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(borderRadius * 0.4),
                        ),
                        child: Text(
                          '-${CurrencyUtils.formatCurrency(sale.discountAmount)}',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: isTablet ? 16.0 : 14.0,
                            color: AppColors.onboardingAccent,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 텍스트 전용 상품 그리드 아이템 위젯 - 이미지 없는 간결한 정사각형 UI
class _TextOnlyProductGridItem extends ConsumerStatefulWidget {
  final Product product;
  final bool isSelected;
  final int quantity;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onDecrease;
  final int columns;
  final bool isEditMode;
  final bool isDeletionMode;
  final bool isCheckedForDeletion;
  final VoidCallback? onCheckToggle;

  const _TextOnlyProductGridItem({
    super.key,
    required this.product,
    required this.isSelected,
    required this.quantity,
    this.onTap,
    this.onLongPress,
    this.onDecrease,
    required this.columns,
    this.isEditMode = false,
    this.isDeletionMode = false,
    this.isCheckedForDeletion = false,
    this.onCheckToggle,
  });

  @override
  ConsumerState<_TextOnlyProductGridItem> createState() => _TextOnlyProductGridItemState();
}

class _TextOnlyProductGridItemState extends ConsumerState<_TextOnlyProductGridItem> {
  /// 수량에 따른 배지 너비 계산 (자릿수에 비례, 좌측 확장 보장)
  double _calculateQuantityBadgeWidth(int quantity, bool isTablet) {
    final digits = quantity.toString().length;
    final charWidth = isTablet ? 10.0 : 9.0; // 문자 폭 근사치
    final horizontalPadding = isTablet ? 8.0 : 6.0;
    final minWidth = isTablet ? 24.0 : 20.0;
    final maxWidth = isTablet ? 80.0 : 64.0;

    final computed = digits * charWidth + horizontalPadding * 2;
    return computed.clamp(minWidth, maxWidth);
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = MediaQuery.of(context).size.width >= 600;
    final borderRadius = isTablet ? 12.0 : 8.0;

    // 카테고리 정보 가져오기
    final categoriesAsync = ref.watch(categoryNotifierProvider);

    // 카테고리 색상 결정
    Color categoryColor = AppColors.secondary; // 기본값
    categoriesAsync.whenData((categories) {
      final category = categories.firstWhere(
        (cat) => cat.id == widget.product.categoryId,
        orElse: () => Category(
          id: widget.product.categoryId,
          name: '기본 카테고리',
          eventId: widget.product.eventId,
          color: AppColors.categoryDefaultColorValue,
        ),
      );
      categoryColor = AppColors.getCategoryColorFromValue(category.color);
    });

    final backgroundColor = categoryColor.withValues(alpha: 0.15);
    final borderColor = categoryColor.withValues(alpha: 0.4);

    return AspectRatio(
      aspectRatio: 1.0, // 정사각형
      child: GestureDetector(
        onTap: widget.onTap,
        onLongPress: widget.onLongPress,
        child: Container(
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(
              color: widget.isSelected ? AppColors.onboardingPrimary : borderColor,
              width: widget.isSelected ? 3 : 2,
            ),
            boxShadow: widget.isSelected ? [
              BoxShadow(
                color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ] : null,
          ),
          child: Stack(
            children: [
              // 메인 컨텐츠 - 완전한 중앙정렬
              Center(
                child: Padding(
                  padding: EdgeInsets.all(isTablet ? 12.0 : 8.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 상품명 (두 줄까지)
                      Flexible(
                        child: Text(
                          widget.product.name,
                          style: TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: isTablet ? 14.0 : 12.0,
                            fontWeight: FontWeight.w600,
                            color: AppColors.onSurface,
                            height: 1.2,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      SizedBox(height: isTablet ? 6 : 4),

                      // 가격 (검정색)
                      Text(
                        CurrencyUtils.formatCurrency(widget.product.price),
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: isTablet ? 13.0 : 11.0,
                          fontWeight: FontWeight.w600,
                          color: AppColors.onSurface, // 검정색
                        ),
                        textAlign: TextAlign.center,
                      ),

                      SizedBox(height: isTablet ? 6 : 4),

                      // 재고수
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: isTablet ? 8.0 : 6.0,
                          vertical: isTablet ? 4.0 : 3.0,
                        ),
                        decoration: BoxDecoration(
                          color: widget.product.isOutOfStock
                            ? AppColors.error.withValues(alpha: 0.15)
                            : AppColors.neutral20, // 회색 배경
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: widget.product.isOutOfStock
                              ? AppColors.error
                              : AppColors.neutral40, // 회색 테두리
                            width: 1,
                          ),
                        ),
                        child: Text(
                          widget.product.isOutOfStock
                            ? '품절'
                            : '재고 ${widget.product.quantity}',
                          style: TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: isTablet ? 10.0 : 9.0,
                            fontWeight: FontWeight.w600,
                            color: widget.product.isOutOfStock
                              ? AppColors.error
                              : AppColors.onSurface, // 검정색
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 선택 수량 표시 (우측 상단) - 동적 크기 조정 (왼쪽으로 확장)
              if (widget.quantity > 0)
                Positioned(
                  top: 4,
                  right: 4,
                  left: null, // 왼쪽 제약 제거
                  child: Align(
                    alignment: Alignment.topRight,
                    child: Container(
                      constraints: BoxConstraints(
                        minWidth: _calculateQuantityBadgeWidth(widget.quantity, isTablet),
                      ),
                    padding: EdgeInsets.symmetric(
                      horizontal: isTablet ? 8 : 6,
                      vertical: isTablet ? 4 : 3,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.onboardingPrimary,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.surface,
                        width: 2,
                      ),
                    ),
                    child: Text(
                      '${widget.quantity}',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: isTablet ? 12 : 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    ),
                  ),
                ),

              // 삭제 모드일 때 체크박스 (좌측 상단)
              if (widget.isDeletionMode)
                Positioned(
                  top: 4,
                  left: 4,
                  child: GestureDetector(
                    onTap: () => widget.onCheckToggle?.call(),
                    child: Container(
                      width: isTablet ? 28 : 24,
                      height: isTablet ? 28 : 24,
                      decoration: BoxDecoration(
                        color: widget.isCheckedForDeletion
                          ? AppColors.onboardingPrimary
                          : Colors.white.withValues(alpha: 0.9),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: widget.isCheckedForDeletion
                            ? AppColors.onboardingPrimary
                            : Colors.grey.shade400,
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        widget.isCheckedForDeletion ? Icons.check : null,
                        color: Colors.white,
                        size: isTablet ? 16 : 14,
                      ),
                    ),
                  ),
                ),

              // 수량 감소 버튼 (하단 중앙)
              if (widget.isSelected && widget.onDecrease != null)
                Positioned(
                  bottom: 4,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: GestureDetector(
                      onTap: widget.onDecrease,
                      child: Container(
                        width: isTablet ? 32 : 28,
                        height: isTablet ? 32 : 28,
                        decoration: BoxDecoration(
                          color: AppColors.error,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppColors.surface,
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          Icons.remove,
                          color: Colors.white,
                          size: isTablet ? 18 : 16,
                        ),
                      ),
                    ),
                  ),
                ),

              // 품절 오버레이
              if (widget.product.isOutOfStock)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppColors.error.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(borderRadius),
                    ),
                    child: Center(
                      child: Text(
                        '품절',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: isTablet ? 16.0 : 14.0,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
